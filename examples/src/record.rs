use std::time::Duration;
use tracing::{info, warn};

use cidre::{arc, av, cat, cf, core_audio as ca, ns, os};

fn main() -> anyhow::Result<()> {
    tracing_subscriber::fmt::init();

    info!("启动 macOS 系统音频捕获 demo");

    // 检查权限提示
    warn!("重要提示：如果程序无法捕获音频，请检查以下权限设置：");
    warn!("1. 系统偏好设置 → 安全性与隐私 → 隐私 → 屏幕录制");
    warn!("2. 确保终端应用（Terminal/iTerm2）已被授权");
    warn!("3. 如果刚刚授权，请重启终端应用");
    info!("继续执行...");

    // 获取默认音频输出设备
    let output_device = ca::System::default_output_device()?;
    let output_uid = output_device.uid()?;

    info!(
        "音频输出设备: {:?}, 采样率: {:?}",
        output_device.name().unwrap_or("Unknown".into()),
        output_device.nominal_sample_rate().unwrap_or(0.0)
    );

    let sub_device = cf::DictionaryOf::with_keys_values(
        &[ca::sub_device_keys::uid()],
        &[output_uid.as_type_ref()],
    );

    // 创建立体声Tap
    let tap_desc = ca::TapDesc::with_stereo_global_tap_excluding_processes(&ns::Array::new());
    let tap = tap_desc.create_process_tap()?;

    info!("Tap创建成功, UID: {:?}", tap.uid());
    let asbd = tap.asbd()?;
    info!("Tap音频格式: 采样率={}, 通道数={}, 位深度={}",
          asbd.sample_rate, asbd.channels_per_frame, asbd.bits_per_channel);

    let sub_tap = cf::DictionaryOf::with_keys_values(
        &[ca::sub_device_keys::uid()],
        &[tap.uid().unwrap().as_type_ref()],
    );

    // 创建聚合设备
    let dict = cf::DictionaryOf::with_keys_values(
        &[
            ca::aggregate_device_keys::is_private(),
            ca::aggregate_device_keys::is_stacked(),
            ca::aggregate_device_keys::tap_auto_start(),
            ca::aggregate_device_keys::name(),
            ca::aggregate_device_keys::main_sub_device(),
            ca::aggregate_device_keys::uid(),
            ca::aggregate_device_keys::sub_device_list(),
            ca::aggregate_device_keys::tap_list(),
        ],
        &[
            cf::Boolean::value_true().as_type_ref(),
            cf::Boolean::value_false(),
            cf::Boolean::value_true(),
            cf::str!(c"AudioCaptureTap"),
            &output_uid,
            &cf::Uuid::new().to_cf_string(),
            &cf::ArrayOf::from_slice(&[sub_device.as_ref()]),
            &cf::ArrayOf::from_slice(&[sub_tap.as_ref()]),
        ],
    );
    let agg_device = ca::AggregateDevice::with_desc(&dict)?;
    info!("聚合设备创建成功");

    // 创建音频格式和文件
    let format = av::AudioFormat::with_asbd(&asbd)
        .ok_or_else(|| anyhow::anyhow!("无法创建音频格式"))?;

    let url = ns::Url::with_fs_path_string(ns::str!(c"/tmp/captured_audio.wav"), false);
    let file = av::AudioFile::open_write_common_format(
        &url,
        &format.settings(),
        av::audio::CommonFormat::PcmF32,
        format.is_interleaved(),
    )?;
    info!("音频文件创建成功: /tmp/captured_audio.wav");

    struct Ctx {
        file: arc::R<av::AudioFile>,
        format: arc::R<av::AudioFormat>,
        sample_count: u64,
        non_zero_count: u64,
        max_amplitude: f32,
    }

    let mut ctx = Ctx {
        file,
        format,
        sample_count: 0,
        non_zero_count: 0,
        max_amplitude: 0.0,
    };

    extern "C" fn audio_proc(
        _device: ca::Device,
        _now: &cat::AudioTimeStamp,
        input_data: &cat::AudioBufList<1>,
        _input_time: &cat::AudioTimeStamp,
        _output_data: &mut cat::AudioBufList<1>,
        _output_time: &cat::AudioTimeStamp,
        ctx: Option<&mut Ctx>,
    ) -> os::Status {
        let ctx = ctx.unwrap();

        if let Some(buf) = av::AudioPcmBuf::with_buf_list_no_copy(&ctx.format, input_data, None) {
            // 统计采样数据
            for channel in 0..buf.format().channel_count() {
                if let Some(data) = buf.data_f32_at(channel as usize) {
                    for &sample in data {
                        ctx.sample_count += 1;
                        let abs_sample = sample.abs();

                        if abs_sample > 0.0001 {
                            ctx.non_zero_count += 1;
                            if abs_sample > ctx.max_amplitude {
                                ctx.max_amplitude = abs_sample;
                            }
                        }
                    }
                }
            }

            // 写入文件
            if let Err(_) = ctx.file.write(&buf) {
                // 静默处理写入错误
            }

            // 每10000个回调输出一次统计
            static mut CALLBACK_COUNT: u64 = 0;
            unsafe {
                CALLBACK_COUNT += 1;
                if CALLBACK_COUNT % 10000 == 0 {
                    let non_zero_percent = if ctx.sample_count > 0 {
                        (ctx.non_zero_count as f64 / ctx.sample_count as f64) * 100.0
                    } else {
                        0.0
                    };
                    println!("回调 #{}: 采样 {}, 非零 {} ({:.1}%), 最大振幅 {:.4}",
                            CALLBACK_COUNT, ctx.sample_count, ctx.non_zero_count,
                            non_zero_percent, ctx.max_amplitude);
                }
            }
        }

        os::Status::NO_ERR
    }

    // 创建音频处理回调并启动设备
    {
        let proc_id = agg_device.create_io_proc_id(audio_proc, Some(&mut ctx))?;
        let _started_device = ca::device_start(agg_device, Some(proc_id))?;
        info!("音频设备启动成功，开始录制...");
        info!("录制中... 10秒后自动停止");
        info!("请确保系统正在播放音频");

        std::thread::sleep(Duration::from_secs(10));
    }

    // 关闭文件并输出统计
    ctx.file.close();

    let non_zero_percent = if ctx.sample_count > 0 {
        (ctx.non_zero_count as f64 / ctx.sample_count as f64) * 100.0
    } else {
        0.0
    };

    info!("录制完成统计:");
    info!("  总采样数: {}", ctx.sample_count);
    info!("  非静音采样: {} ({:.1}%)", ctx.non_zero_count, non_zero_percent);
    info!("  最大振幅: {:.4}", ctx.max_amplitude);
    info!("音频文件已保存为: /tmp/captured_audio.wav");

    if ctx.non_zero_count == 0 {
        warn!("警告: 录制的音频全部为静音，请检查:");
        warn!("  1. 系统是否正在播放音频");
        warn!("  2. 音频输出设备是否正确");
        warn!("  3. 系统音频权限是否已授予");
    }

    Ok(())
}



