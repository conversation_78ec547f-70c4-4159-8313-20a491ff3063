use std::sync::{<PERSON>, <PERSON>tex};
use std::task::{<PERSON>, Waker};
use std::fs::File;
use std::io::Write;

use anyhow::Result;
use futures_util::{Stream, StreamExt};
use ringbuf::{
    traits::{Consumer, Producer, Split},
    HeapC<PERSON>, HeapProd, HeapRb,
};
use tokio::time::{Duration, Instant};
use tracing::{info, warn};

use cidre::{arc, av, cat, cf, core_audio as ca, ns, os};

struct WakerState {
    waker: Option<Waker>,
    has_data: bool,
}

pub struct SpeakerStream {
    consumer: HeapCons<f32>,
    stream_desc: cat::AudioBasicStreamDesc,
    _device: ca::hardware::StartedDevice<ca::AggregateDevice>,
    _ctx: Box<Ctx>,
    _tap: ca::TapGuard,
    waker_state: Arc<Mutex<WakerState>>,
}

impl SpeakerStream {
    pub fn sample_rate(&self) -> u32 {
        self.stream_desc.sample_rate as u32
    }
}

struct Ctx {
    format: arc::R<av::AudioFormat>,
    producer: <PERSON>ap<PERSON><PERSON><f32>,
    waker_state: Arc<Mutex<WakerState>>,
}

pub struct SpeakerInput {
    tap: ca::TapGuard,
    agg_desc: arc::Retained<cf::DictionaryOf<cf::String, cf::Type>>,
}

impl SpeakerInput {
    pub fn new() -> Result<Self> {
        let output_device = ca::System::default_output_device()?;
        let output_uid = output_device.uid()?;

        tracing::info!(
            name = ?output_device.name().unwrap_or("Unknown Speaker".into()),
            nominal_sample_rate = ?output_device.nominal_sample_rate().unwrap(),
            actual_sample_rate = ?output_device.actual_sample_rate().unwrap(),
            "speaker_output_device"
        );

        let sub_device = cf::DictionaryOf::with_keys_values(
            &[ca::sub_device_keys::uid()],
            &[output_uid.as_type_ref()],
        );

        let tap_desc = ca::TapDesc::with_mono_global_tap_excluding_processes(&ns::Array::new());
        let tap = tap_desc.create_process_tap()?;

        let sub_tap = cf::DictionaryOf::with_keys_values(
            &[ca::sub_device_keys::uid()],
            &[tap.uid().unwrap().as_type_ref()],
        );

        let agg_desc = cf::DictionaryOf::with_keys_values(
            &[
                ca::aggregate_device_keys::is_private(),
                ca::aggregate_device_keys::is_stacked(),
                ca::aggregate_device_keys::tap_auto_start(),
                ca::aggregate_device_keys::name(),
                ca::aggregate_device_keys::main_sub_device(),
                ca::aggregate_device_keys::uid(),
                ca::aggregate_device_keys::sub_device_list(),
                ca::aggregate_device_keys::tap_list(),
            ],
            &[
                cf::Boolean::value_true().as_type_ref(),
                cf::Boolean::value_false(),
                cf::Boolean::value_true(),
                cf::str!(c"hypr-audio-tap"),
                &output_uid,
                &cf::Uuid::new().to_cf_string(),
                &cf::ArrayOf::from_slice(&[sub_device.as_ref()]),
                &cf::ArrayOf::from_slice(&[sub_tap.as_ref()]),
            ],
        );

        Ok(Self { tap, agg_desc })
    }

    fn start_device(
        &self,
        ctx: &mut Box<Ctx>,
    ) -> Result<ca::hardware::StartedDevice<ca::AggregateDevice>> {
        extern "C" fn proc(
            _device: ca::Device,
            _now: &cat::AudioTimeStamp,
            input_data: &cat::AudioBufList<1>,
            _input_time: &cat::AudioTimeStamp,
            _output_data: &mut cat::AudioBufList<1>,
            _output_time: &cat::AudioTimeStamp,
            ctx: Option<&mut Ctx>,
        ) -> os::Status {
            let ctx = ctx.unwrap();

            assert_eq!(ctx.format.common_format(), av::audio::CommonFormat::PcmF32);

            if let Some(view) =
                av::AudioPcmBuf::with_buf_list_no_copy(&ctx.format, input_data, None)
            {
                if let Some(data) = view.data_f32_at(0) {
                    let buffer_size = data.len();
                    let pushed = ctx.producer.push_slice(data);
                    if pushed < buffer_size {
                        tracing::warn!("macos_speaker_dropped_{}_samples", buffer_size - pushed,);
                    }

                    let mut waker_state = ctx.waker_state.lock().unwrap();
                    if pushed > 0 && !waker_state.has_data {
                        waker_state.has_data = true;
                        if let Some(waker) = waker_state.waker.take() {
                            drop(waker_state);
                            waker.wake();
                        }
                    }
                }
            } else {
                tracing::warn!("macos_speaker_empty_buffer");
            }

            os::Status::NO_ERR
        }

        let agg_device = ca::AggregateDevice::with_desc(&self.agg_desc)?;
        let proc_id = agg_device.create_io_proc_id(proc, Some(ctx))?;
        let started_device = ca::device_start(agg_device, Some(proc_id))?;

        Ok(started_device)
    }

    pub fn stream(self) -> Result<SpeakerStream> {
        let asbd = self.tap.asbd().unwrap();
        let format = av::AudioFormat::with_asbd(&asbd).unwrap();

        let rb = HeapRb::<f32>::new(1024 * 16);
        let (producer, consumer) = rb.split();

        let waker_state = Arc::new(Mutex::new(WakerState {
            waker: None,
            has_data: false,
        }));

        let mut ctx = Box::new(Ctx {
            format,
            producer,
            waker_state: waker_state.clone(),
        });

        let device = self.start_device(&mut ctx)?;

        Ok(SpeakerStream {
            consumer,
            stream_desc: asbd,
            _device: device,
            _ctx: ctx,
            _tap: self.tap,
            waker_state,
        })
    }
}

impl Stream for SpeakerStream {
    type Item = f32;

    fn poll_next(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> Poll<Option<Self::Item>> {
        if let Some(sample) = self.consumer.try_pop() {
            return Poll::Ready(Some(sample));
        }

        {
            let mut state = self.waker_state.lock().unwrap();
            state.has_data = false;
            state.waker = Some(cx.waker().clone());
            drop(state);
        }

        match self.consumer.try_pop() {
            Some(sample) => Poll::Ready(Some(sample)),
            None => Poll::Pending,
        }
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::fmt::init();
    
    info!("启动 macOS 系统音频捕获 demo");
    
    let speaker_input = SpeakerInput::new()?;
    let mut audio_stream = speaker_input.stream()?;
    
    let sample_rate = audio_stream.sample_rate();
    info!("音频采样率: {} Hz", sample_rate);
    
    let audio_file = Arc::new(Mutex::new(
        File::create("captured_audio.pcm")?
    ));
    
    let mut asr_buffer = Vec::with_capacity(sample_rate as usize);
    let mut last_asr_time = Instant::now();
    
    info!("开始捕获音频...");
    
    while let Some(sample) = audio_stream.next().await {
        // 实时写入文件
        {
            let mut file = audio_file.lock().unwrap();
            let bytes = sample.to_le_bytes();
            if let Err(e) = file.write_all(&bytes) {
                warn!("写入音频文件失败: {}", e);
            }
        }
        
        // 收集 ASR 缓冲区数据
        asr_buffer.push(sample);
        
        // 每秒进行一次 ASR 处理
        if last_asr_time.elapsed() >= Duration::from_secs(1) {
            if !asr_buffer.is_empty() {
                let buffer_clone = asr_buffer.clone();
                let sample_rate_clone = sample_rate;
                
                tokio::spawn(async move {
                    perform_asr(buffer_clone, sample_rate_clone).await;
                });
                
                asr_buffer.clear();
                last_asr_time = Instant::now();
            }
        }
        
        // 限制缓冲区大小
        if asr_buffer.len() > sample_rate as usize * 2 {
            asr_buffer.drain(0..sample_rate as usize);
        }
    }
    
    Ok(())
}

async fn perform_asr(audio_samples: Vec<f32>, sample_rate: u32) {
    info!("开始 ASR 处理，音频长度: {:.2}秒", 
          audio_samples.len() as f32 / sample_rate as f32);
    
    // 模拟处理时间
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // 检查是否有足够的音频能量
    let has_speech = audio_samples.iter().any(|&sample| sample.abs() > 0.01);
    
    if has_speech {
        info!("ASR 结果: 检测到语音活动");
    }
}