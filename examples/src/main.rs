use std::sync::{<PERSON>, <PERSON>tex};
use std::task::{<PERSON>, Waker};

use anyhow::Result;
use futures_util::{Stream, StreamExt};
use ringbuf::{
    traits::{Consumer, Producer, Split},
    He<PERSON><PERSON><PERSON>, Heap<PERSON><PERSON>, HeapRb,
};
use tokio::time::{Duration, Instant};
use tokio::signal;
use tracing::{info, warn};

use cidre::{arc, av, cat, cf, core_audio as ca, ns, os};

struct WakerState {
    waker: Option<Waker>,
    has_data: bool,
}

pub struct SpeakerStream {
    consumer: HeapCons<f32>,
    stream_desc: cat::AudioBasicStreamDesc,
    _device: ca::hardware::StartedDevice<ca::AggregateDevice>,
    _ctx: Box<Ctx>,
    _tap: ca::TapGuard,
    waker_state: Arc<Mutex<WakerState>>,
}

impl SpeakerStream {
    pub fn sample_rate(&self) -> u32 {
        self.stream_desc.sample_rate as u32
    }
}

struct Ctx {
    format: arc::R<av::AudioFormat>,
    producer: <PERSON><PERSON><PERSON><PERSON><f32>,
    waker_state: Arc<Mutex<WakerState>>,
}

pub struct SpeakerInput {
    tap: ca::TapGuard,
    agg_desc: arc::Retained<cf::DictionaryOf<cf::String, cf::Type>>,
}

impl SpeakerInput {
    pub fn new() -> Result<Self> {
        let output_device = ca::System::default_output_device()?;
        let output_uid = output_device.uid()?;

        tracing::info!(
            name = ?output_device.name().unwrap_or("Unknown Speaker".into()),
            nominal_sample_rate = ?output_device.nominal_sample_rate().unwrap(),
            actual_sample_rate = ?output_device.actual_sample_rate().unwrap(),
            "speaker_output_device"
        );

        let sub_device = cf::DictionaryOf::with_keys_values(
            &[ca::sub_device_keys::uid()],
            &[output_uid.as_type_ref()],
        );

        let tap_desc = ca::TapDesc::with_mono_global_tap_excluding_processes(&ns::Array::new());
        let tap = tap_desc.create_process_tap()?;

        let sub_tap = cf::DictionaryOf::with_keys_values(
            &[ca::sub_device_keys::uid()],
            &[tap.uid().unwrap().as_type_ref()],
        );

        let agg_desc = cf::DictionaryOf::with_keys_values(
            &[
                ca::aggregate_device_keys::is_private(),
                ca::aggregate_device_keys::is_stacked(),
                ca::aggregate_device_keys::tap_auto_start(),
                ca::aggregate_device_keys::name(),
                ca::aggregate_device_keys::main_sub_device(),
                ca::aggregate_device_keys::uid(),
                ca::aggregate_device_keys::sub_device_list(),
                ca::aggregate_device_keys::tap_list(),
            ],
            &[
                cf::Boolean::value_true().as_type_ref(),
                cf::Boolean::value_false(),
                cf::Boolean::value_true(),
                cf::str!(c"hypr-audio-tap"),
                &output_uid,
                &cf::Uuid::new().to_cf_string(),
                &cf::ArrayOf::from_slice(&[sub_device.as_ref()]),
                &cf::ArrayOf::from_slice(&[sub_tap.as_ref()]),
            ],
        );

        Ok(Self { tap, agg_desc })
    }

    fn start_device(
        &self,
        ctx: &mut Box<Ctx>,
    ) -> Result<ca::hardware::StartedDevice<ca::AggregateDevice>> {
        extern "C" fn proc(
            _device: ca::Device,
            _now: &cat::AudioTimeStamp,
            input_data: &cat::AudioBufList<1>,
            _input_time: &cat::AudioTimeStamp,
            _output_data: &mut cat::AudioBufList<1>,
            _output_time: &cat::AudioTimeStamp,
            ctx: Option<&mut Ctx>,
        ) -> os::Status {
            let ctx = ctx.unwrap();

            assert_eq!(ctx.format.common_format(), av::audio::CommonFormat::PcmF32);

            if let Some(view) =
                av::AudioPcmBuf::with_buf_list_no_copy(&ctx.format, input_data, None)
            {
                if let Some(data) = view.data_f32_at(0) {
                    let buffer_size = data.len();

                    // 调试：检查数据内容
                    let max_sample = data.iter().map(|&x| x.abs()).fold(0.0f32, f32::max);
                    let non_zero_count = data.iter().filter(|&&x| x.abs() > 0.0001).count();

                    // 每1000次回调输出一次调试信息
                    static mut CALLBACK_COUNT: u64 = 0;
                    unsafe {
                        CALLBACK_COUNT += 1;
                        if CALLBACK_COUNT % 1000 == 0 {
                            tracing::info!("音频回调 #{}: 缓冲区大小={}, 非零采样={}, 最大振幅={:.4}",
                                         CALLBACK_COUNT, buffer_size, non_zero_count, max_sample);
                        }
                    }

                    let pushed = ctx.producer.push_slice(data);
                    if pushed < buffer_size {
                        tracing::warn!("macos_speaker_dropped_{}_samples", buffer_size - pushed,);
                    }

                    let mut waker_state = ctx.waker_state.lock().unwrap();
                    if pushed > 0 && !waker_state.has_data {
                        waker_state.has_data = true;
                        if let Some(waker) = waker_state.waker.take() {
                            drop(waker_state);
                            waker.wake();
                        }
                    }
                }
            } else {
                tracing::warn!("macos_speaker_empty_buffer");
            }

            os::Status::NO_ERR
        }

        let agg_device = ca::AggregateDevice::with_desc(&self.agg_desc)?;
        let proc_id = agg_device.create_io_proc_id(proc, Some(ctx))?;
        let started_device = ca::device_start(agg_device, Some(proc_id))?;

        Ok(started_device)
    }

    pub fn stream(self) -> Result<SpeakerStream> {
        let asbd = self.tap.asbd().unwrap();
        let format = av::AudioFormat::with_asbd(&asbd).unwrap();

        let rb = HeapRb::<f32>::new(1024 * 16);
        let (producer, consumer) = rb.split();

        let waker_state = Arc::new(Mutex::new(WakerState {
            waker: None,
            has_data: false,
        }));

        let mut ctx = Box::new(Ctx {
            format,
            producer,
            waker_state: waker_state.clone(),
        });

        let device = self.start_device(&mut ctx)?;

        Ok(SpeakerStream {
            consumer,
            stream_desc: asbd,
            _device: device,
            _ctx: ctx,
            _tap: self.tap,
            waker_state,
        })
    }
}

impl Stream for SpeakerStream {
    type Item = f32;

    fn poll_next(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> Poll<Option<Self::Item>> {
        if let Some(sample) = self.consumer.try_pop() {
            return Poll::Ready(Some(sample));
        }

        {
            let mut state = self.waker_state.lock().unwrap();
            state.has_data = false;
            state.waker = Some(cx.waker().clone());
            drop(state);
        }

        match self.consumer.try_pop() {
            Some(sample) => Poll::Ready(Some(sample)),
            None => Poll::Pending,
        }
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::fmt::init();
    
    info!("启动 macOS 系统音频捕获 demo");
    
    let speaker_input = SpeakerInput::new()?;
    let mut audio_stream = speaker_input.stream()?;
    
    let raw_sample_rate = audio_stream.sample_rate();
    let sample_rate = {
        let rounded = if raw_sample_rate > 47000 && raw_sample_rate < 49000 {
            48000
        } else if raw_sample_rate > 43000 && raw_sample_rate < 45000 {
            44100
        } else {
            raw_sample_rate
        };
        info!("原始采样率: {} Hz，使用标准采样率: {} Hz", raw_sample_rate, rounded);
        rounded
    };

    // 创建WAV文件写入器
    let wav_spec = hound::WavSpec {
        channels: 1,
        sample_rate,
        bits_per_sample: 32,
        sample_format: hound::SampleFormat::Float,
    };
    let mut wav_writer = hound::WavWriter::create("captured_audio.wav", wav_spec)?;
    
    info!("开始捕获音频...");
    info!("按 Ctrl+C 停止录制");
    info!("请确保系统正在播放音频以便捕获");

    // 创建一个用于优雅关闭的信号处理
    let mut sigint = signal::unix::signal(signal::unix::SignalKind::interrupt())?;

    // 统计变量
    let mut total_samples = 0u64;
    let mut non_zero_samples = 0u64;
    let mut max_amplitude = 0.0f32;
    let mut last_log_time = Instant::now();

    loop {
        tokio::select! {
            sample = audio_stream.next() => {
                match sample {
                    Some(sample) => {
                        // 检查采样是否有效
                        if sample.is_nan() || sample.is_infinite() {
                            warn!("检测到无效采样: {}", sample);
                            continue;
                        }

                        // 统计采样
                        total_samples += 1;
                        let abs_sample = sample.abs();
                        if abs_sample > 0.0001 {
                            non_zero_samples += 1;
                            if abs_sample > max_amplitude {
                                max_amplitude = abs_sample;
                            }
                        }

                        // 写入WAV文件
                        if let Err(e) = wav_writer.write_sample(sample) {
                            warn!("写入WAV文件失败: {}", e);
                        }

                        // 每5秒输出一次统计信息
                        if last_log_time.elapsed() >= Duration::from_secs(5) {
                            let non_zero_percent = if total_samples > 0 {
                                (non_zero_samples as f64 / total_samples as f64) * 100.0
                            } else {
                                0.0
                            };
                            info!("统计: 总采样 {}, 非静音 {} ({:.1}%), 最大振幅 {:.4}",
                                  total_samples, non_zero_samples, non_zero_percent, max_amplitude);
                            last_log_time = Instant::now();
                        }
                    }
                    None => {
                        info!("音频流结束");
                        break;
                    }
                }
            }
            _ = sigint.recv() => {
                info!("接收到中断信号，正在停止录制...");
                break;
            }
        }
    }

    // 输出最终统计
    let non_zero_percent = if total_samples > 0 {
        (non_zero_samples as f64 / total_samples as f64) * 100.0
    } else {
        0.0
    };
    info!("录制完成统计:");
    info!("  总采样数: {}", total_samples);
    info!("  非静音采样: {} ({:.1}%)", non_zero_samples, non_zero_percent);
    info!("  最大振幅: {:.4}", max_amplitude);

    // 关闭WAV文件
    if let Err(e) = wav_writer.finalize() {
        warn!("关闭WAV文件失败: {}", e);
    } else {
        info!("WAV文件已保存为 captured_audio.wav");
        if non_zero_samples == 0 {
            warn!("警告: 录制的音频全部为静音，请检查:");
            warn!("  1. 系统是否正在播放音频");
            warn!("  2. 音频输出设备是否正确");
            warn!("  3. 系统音频权限是否已授予");
        }
    }
    
    Ok(())
}

