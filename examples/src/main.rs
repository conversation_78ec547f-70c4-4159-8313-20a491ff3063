use std::time::Duration;
use tracing::{info, warn};

use cidre::{arc, av, cat, cf, core_audio as ca, ns, os};

struct AudioRecorder {
    _tap: ca::TapGuard,
    _started_device: ca::hardware::StartedDevice<ca::AggregateDevice>,
}

// 使用全局状态来避免生命周期问题
static mut WAV_WRITER: Option<hound::WavWriter<std::io::BufWriter<std::fs::File>>> = None;
static mut SAMPLE_COUNT: u64 = 0;
static mut NON_ZERO_COUNT: u64 = 0;
static mut MAX_AMPLITUDE: f32 = 0.0;

impl AudioRecorder {
    pub fn new() -> anyhow::Result<Self> {
        info!("获取默认音频输出设备...");
        let output_device = ca::System::default_output_device()?;
        let output_uid = output_device.uid()?;

        info!(
            "音频输出设备: {:?}, 采样率: {:?}",
            output_device.name().unwrap_or("Unknown".into()),
            output_device.nominal_sample_rate().unwrap_or(0.0)
        );

        let sub_device = cf::DictionaryOf::with_keys_values(
            &[ca::sub_device_keys::uid()],
            &[output_uid.as_type_ref()],
        );

        info!("创建音频Tap...");
        // 使用立体声Tap，这是关键差异
        let tap_desc = ca::TapDesc::with_stereo_global_tap_excluding_processes(&ns::Array::new());
        let tap = tap_desc.create_process_tap()?;

        info!("Tap创建成功, UID: {:?}", tap.uid());
        let asbd = tap.asbd()?;
        info!("Tap音频格式: 采样率={}, 通道数={}, 位深度={}",
              asbd.sample_rate, asbd.channels_per_frame, asbd.bits_per_channel);

        let sub_tap = cf::DictionaryOf::with_keys_values(
            &[ca::sub_device_keys::uid()],
            &[tap.uid().unwrap().as_type_ref()],
        );

        info!("创建聚合设备...");
        let agg_desc = cf::DictionaryOf::with_keys_values(
            &[
                ca::aggregate_device_keys::is_private(),
                ca::aggregate_device_keys::is_stacked(),
                ca::aggregate_device_keys::tap_auto_start(),
                ca::aggregate_device_keys::name(),
                ca::aggregate_device_keys::main_sub_device(),
                ca::aggregate_device_keys::uid(),
                ca::aggregate_device_keys::sub_device_list(),
                ca::aggregate_device_keys::tap_list(),
            ],
            &[
                cf::Boolean::value_true().as_type_ref(),
                cf::Boolean::value_false(),
                cf::Boolean::value_true(),
                cf::str!(c"AudioCaptureTap"),
                &output_uid,
                &cf::Uuid::new().to_cf_string(),
                &cf::ArrayOf::from_slice(&[sub_device.as_ref()]),
                &cf::ArrayOf::from_slice(&[sub_tap.as_ref()]),
            ],
        );

        let agg_device = ca::AggregateDevice::with_desc(&agg_desc)?;
        info!("聚合设备创建成功");

        // 创建WAV文件
        let format = av::AudioFormat::with_asbd(&asbd)?;
        let sample_rate = asbd.sample_rate as u32;
        let channels = asbd.channels_per_frame as u16;

        let wav_spec = hound::WavSpec {
            channels,
            sample_rate,
            bits_per_sample: 32,
            sample_format: hound::SampleFormat::Float,
        };
        let wav_writer = hound::WavWriter::create("captured_audio.wav", wav_spec)?;
        info!("WAV文件创建成功: {} Hz, {} 通道", sample_rate, channels);

        // 初始化全局状态
        unsafe {
            WAV_WRITER = Some(wav_writer);
            SAMPLE_COUNT = 0;
            NON_ZERO_COUNT = 0;
            MAX_AMPLITUDE = 0.0;
        }

        extern "C" fn audio_proc(
            _device: ca::Device,
            _now: &cat::AudioTimeStamp,
            input_data: &cat::AudioBufList<1>,
            _input_time: &cat::AudioTimeStamp,
            _output_data: &mut cat::AudioBufList<1>,
            _output_time: &cat::AudioTimeStamp,
            format_ptr: Option<&mut arc::R<av::AudioFormat>>,
        ) -> os::Status {
            let format = format_ptr.unwrap();

            if let Some(buf) = av::AudioPcmBuf::with_buf_list_no_copy(format, input_data, None) {
                unsafe {
                    if let Some(ref mut writer) = WAV_WRITER {
                        // 处理每个通道的数据
                        for channel in 0..buf.format().channel_count() {
                            if let Some(data) = buf.data_f32_at(channel) {
                                for &sample in data {
                                    SAMPLE_COUNT += 1;
                                    let abs_sample = sample.abs();

                                    if abs_sample > 0.0001 {
                                        NON_ZERO_COUNT += 1;
                                        if abs_sample > MAX_AMPLITUDE {
                                            MAX_AMPLITUDE = abs_sample;
                                        }
                                    }

                                    let _ = writer.write_sample(sample);
                                }
                            }
                        }

                        // 每10000个回调输出一次统计
                        static mut CALLBACK_COUNT: u64 = 0;
                        CALLBACK_COUNT += 1;
                        if CALLBACK_COUNT % 10000 == 0 {
                            let non_zero_percent = if SAMPLE_COUNT > 0 {
                                (NON_ZERO_COUNT as f64 / SAMPLE_COUNT as f64) * 100.0
                            } else {
                                0.0
                            };
                            println!("回调 #{}: 采样 {}, 非零 {} ({:.1}%), 最大振幅 {:.4}",
                                    CALLBACK_COUNT, SAMPLE_COUNT, NON_ZERO_COUNT,
                                    non_zero_percent, MAX_AMPLITUDE);
                        }
                    }
                }
            }

            os::Status::NO_ERR
        }

        info!("创建音频处理回调...");
        let mut format_for_callback = format;
        let proc_id = agg_device.create_io_proc_id(audio_proc, Some(&mut format_for_callback))?;

        info!("启动音频设备...");
        let started_device = ca::device_start(agg_device, Some(proc_id))?;
        info!("音频设备启动成功，开始录制...");

        Ok(Self {
            _tap: tap,
            _started_device: started_device,
        })
    }



fn main() -> anyhow::Result<()> {
    tracing_subscriber::fmt::init();

    info!("启动 macOS 系统音频捕获 demo");

    let _recorder = AudioRecorder::new()?;

    info!("录制中... 按 Ctrl+C 停止");
    info!("请确保系统正在播放音频");

    // 简单的阻塞等待，让音频在后台录制
    std::thread::sleep(Duration::from_secs(10));

    info!("录制完成，文件已保存为 captured_audio.wav");

    Ok(())
}

