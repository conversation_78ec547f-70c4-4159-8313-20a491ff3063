{"rustc": 12610991425282158916, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 336243669335521001, "path": 1861857876847578479, "deps": [[1615478164327904835, "pin_utils", false, 15494409939754333919], [1906322745568073236, "pin_project_lite", false, 1458207731257540291], [5451793922601807560, "slab", false, 7791177159884213446], [7620660491849607393, "futures_core", false, 4257986804481987881], [10565019901765856648, "futures_macro", false, 1960221676518045631], [16240732885093539806, "futures_task", false, 13739774425467355619]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-d29adff9d5caa1c9/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}