{"rustc": 12610991425282158916, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 6231153853055262910, "deps": [[1017461770342116999, "sharded_slab", false, 9490144318611439683], [1359731229228270592, "thread_local", false, 4700487420965003982], [3424551429995674438, "tracing_core", false, 8889951926428751791], [3666196340704888985, "smallvec", false, 5521846010396975515], [8614575489689151157, "nu_ansi_term", false, 360156671185100537], [10806489435541507125, "tracing_log", false, 17194673822588622155]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-765ce8c47be2c0f9/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}